模式,进程数,每个进程的线程数,并发数据包(CP),最多成功数据包
sync,1,1,1,1
sync,1,1,2,1
sync,1,1,3,1
sync,1,1,4,1
sync,1,1,5,1
sync,1,1,6,1
sync,1,1,7,1
sync,1,1,8,1
sync,1,1,9,1
sync,1,1,10,1
sync,2,1,1,1
sync,2,1,2,2
sync,2,1,3,2
sync,2,1,4,2
sync,2,1,5,2
sync,2,1,6,2
sync,2,1,7,2
sync,2,1,8,2
sync,2,1,9,2
sync,2,1,10,2
sync,3,1,1,1
sync,3,1,2,2
sync,3,1,3,3
sync,3,1,4,3
sync,3,1,5,3
sync,3,1,6,3
sync,3,1,7,3
sync,3,1,8,3
sync,3,1,9,3
sync,3,1,10,3
sync,4,1,1,1
sync,4,1,2,2
sync,4,1,3,3
sync,4,1,4,4
sync,4,1,5,4
sync,4,1,6,4
sync,4,1,7,4
sync,4,1,8,4
sync,4,1,9,4
sync,4,1,10,4
sync,5,1,1,1
sync,5,1,2,2
sync,5,1,3,3
sync,5,1,4,4
sync,5,1,5,5
sync,5,1,6,5
sync,5,1,7,5
sync,5,1,8,5
sync,5,1,9,5
sync,5,1,10,5
sync,10,1,1,1
sync,10,1,2,2
sync,10,1,3,3
sync,10,1,4,4
sync,10,1,5,5
sync,10,1,6,6
sync,10,1,7,7
sync,10,1,8,8
sync,10,1,9,9
sync,10,1,10,10
gthread,2,2,1,1
gthread,2,2,2,2
gthread,2,2,3,3
gthread,2,2,4,3
gthread,2,2,5,4
gthread,2,2,6,4
gthread,2,3,1,1
gthread,2,3,2,2
gthread,2,3,3,3
gthread,2,3,4,4
gthread,2,3,5,4
gthread,2,3,6,5
gthread,2,3,7,6
gthread,3,2,1,1
gthread,3,2,2,2
gthread,3,2,3,2
gthread,3,2,4,3
gthread,3,2,5,5
gthread,3,2,6,4
gthread,3,2,7,4
gevent,1,1,1,1
gevent,1,1,5,5
gevent,1,1,10,10
gevent,1,1,20,20
gevent,1,1,50,22
gevent,1,1,100,16
gevent,1,1,150,15
gevent,1,1,200,0
