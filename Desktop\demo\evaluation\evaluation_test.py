#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
研究同步和异步条件对数据包最大成功利用率的影响
"""

import requests
import subprocess
import time
import threading
from concurrent.futures import ThreadPoolExecutor
import os
import sys
import csv
from dataclasses import dataclass
from typing import List

@dataclass
class TestResult:
    mode: str
    processes: int
    threads_per_process: int
    concurrent_packets: int
    successful_packets: int

class EvaluationTest:
    def __init__(self):
        self.results: List[TestResult] = []
        self.base_url = "http://127.0.0.1"
        
    def start_server(self, mode: str, workers: int, threads: int = 1, port: int = 8000):
        """启动服务器"""
        if mode == "sync":
            cmd = [
                "gunicorn", "-w", str(workers), "-b", f"127.0.0.1:{port}",
                "--worker-class", "sync", "--timeout", "30", "wsgi:application"
            ]
        elif mode == "gthread":
            cmd = [
                "gunicorn", "-w", str(workers), "--threads", str(threads),
                "-b", f"127.0.0.1:{port}", "--worker-class", "gthread",
                "--timeout", "30", "wsgi:application"
            ]
        elif mode == "gevent":
            cmd = [
                "gunicorn", "-w", str(workers), "-b", f"127.0.0.1:{port}",
                "--worker-class", "gevent", "--timeout", "30", "wsgi:application"
            ]
        
        print(f"启动服务器: {' '.join(cmd)}")
        process = subprocess.Popen(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        time.sleep(4)  # 等待服务器启动
        
        # 检查服务器是否启动成功
        try:
            response = requests.get(f"http://127.0.0.1:{port}/", timeout=5)
            if response.status_code == 200:
                print(f"服务器启动成功")
                return process
        except:
            pass
        
        print(f"服务器启动失败")
        process.terminate()
        return None
    
    def stop_server(self, process):
        """停止服务器"""
        if process:
            try:
                process.terminate()
                process.wait(timeout=5)
            except:
                pass
            time.sleep(1)
    
    def test_concurrent_requests(self, port: int, concurrent: int):
        """测试并发请求"""
        # 重置数据
        try:
            requests.post(f"http://127.0.0.1:{port}/api/reset", timeout=5)
        except:
            pass
        
        # 设置测试优惠码
        try:
            requests.post(f"http://127.0.0.1:{port}/api/setup_test_coupon", 
                        json={"code": "TEST", "max_uses": 1, "discount": 10}, timeout=5)
        except:
            pass
        
        # 发送并发请求
        def send_request(i):
            try:
                response = requests.post(
                    f"http://127.0.0.1:{port}/redeem/unsafe",
                    json={"coupon_code": "TEST", "user_id": f"user_{i}"},
                    timeout=10
                )
                return response.json()
            except:
                return {"success": False}
        
        with ThreadPoolExecutor(max_workers=concurrent) as executor:
            futures = [executor.submit(send_request, i) for i in range(concurrent)]
            results = [f.result() for f in futures]
        
        successful = sum(1 for r in results if r.get('success', False))
        return successful
    
    def sync_mode(self):
        """同步模式"""
        print("\n同步模式...")
        
        test_cases = [
            (1, [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]),
            (2, [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]),
            (3, [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]),
            (4, [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]),
            (5, [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]),
            (10, [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]),
        ]
        
        for workers, concurrent_list in test_cases:
            print(f"\n测试 sync 模式: {workers}进程")
            
            server_process = self.start_server("sync", workers, 1, 8000)
            if not server_process:
                continue
            
            try:
                for concurrent in concurrent_list:
                    successful = self.test_concurrent_requests(8000, concurrent)
                    
                    result = TestResult(
                        mode="sync",
                        processes=workers,
                        threads_per_process=1,
                        concurrent_packets=concurrent,
                        successful_packets=successful
                    )
                    self.results.append(result)
                    
                    print(f"   CP={concurrent}: {successful}/{concurrent} 成功")
                    
                    time.sleep(0.5)
                    
            finally:
                self.stop_server(server_process)
    
    def gthread_mode(self):
        """多线程同步模式"""
        print("\n多线程同步模式...")
        
        test_cases = [
            (2, 2, [1, 2, 3, 4, 5, 6]),
            (2, 3, [1, 2, 3, 4, 5, 6, 7]),
            (3, 2, [1, 2, 3, 4, 5, 6, 7]),
        ]
        
        for workers, threads, concurrent_list in test_cases:
            print(f"\n测试 gthread 模式: {workers}进程 × {threads}线程")
            
            server_process = self.start_server("gthread", workers, threads, 8001)
            if not server_process:
                continue
            
            try:
                for concurrent in concurrent_list:
                    successful = self.test_concurrent_requests(8001, concurrent)
                    
                    result = TestResult(
                        mode="gthread",
                        processes=workers,
                        threads_per_process=threads,
                        concurrent_packets=concurrent,
                        successful_packets=successful
                    )
                    self.results.append(result)
                    
                    print(f"   CP={concurrent}: {successful}/{concurrent} 成功")
                    
                    time.sleep(0.5)
                    
            finally:
                self.stop_server(server_process)
    
    def gevent_mode(self):
        """异步模式"""
        print("\n异步模式...")
        
        concurrent_list = [1, 5, 10, 20, 50, 100, 150, 200]
        
        server_process = self.start_server("gevent", 1, 1, 8002)
        if not server_process:
            return
        
        try:
            for concurrent in concurrent_list:
                successful = self.test_concurrent_requests(8002, concurrent)
                
                result = TestResult(
                    mode="gevent",
                    processes=1,
                    threads_per_process=1,
                    concurrent_packets=concurrent,
                    successful_packets=successful
                )
                self.results.append(result)
                
                print(f"   CP={concurrent}: {successful}/{concurrent} 成功")
                
                time.sleep(0.5)
                
        finally:
            self.stop_server(server_process)
    
    def generate_table(self):
        """生成表格和图表"""
        print("\n研究同步和异步条件对数据包最大成功利用率的影响")
        print("=" * 80)
        
        # 打印表格
        print(f"{'模式':<8} {'进程数':<6} {'每个进程的线程数':<12} {'并发数据包(CP)':<12} {'最多成功数据包':<12}")
        print("-" * 80)
        
        for result in self.results:
            print(f"{result.mode:<8} {result.processes:<6} {result.threads_per_process:<12} "
                  f"{result.concurrent_packets:<12} {result.successful_packets:<12}")
        
        # 保存结果
        with open('evaluation_results.csv', 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['模式', '进程数', '每个进程的线程数', '并发数据包(CP)', '最多成功数据包'])
            
            for result in self.results:
                writer.writerow([
                    result.mode, result.processes, result.threads_per_process,
                    result.concurrent_packets, result.successful_packets
                ])
        
        print(f"\n详细结果已保存到: evaluation_results.csv")
        
        # 生成图表
        self.generate_charts()
    
    def generate_charts(self):
        """生成可视化图表"""
        try:
            import matplotlib.pyplot as plt
            import pandas as pd
            import matplotlib.font_manager as fm

            # 配置中文字体支持
            # 尝试多种中文字体，确保在不同系统上都能正常显示
            chinese_fonts = [
                'SimHei',           # Windows 黑体
                'Microsoft YaHei',  # Windows 微软雅黑
                'PingFang SC',      # macOS 苹方
                'Hiragino Sans GB', # macOS 冬青黑体
                'WenQuanYi Micro Hei', # Linux 文泉驿微米黑
                'WenQuanYi Zen Hei', # Linux 文泉驿正黑
                'Noto Sans CJK SC', # Google Noto 字体
                'Noto Sans CJK',    # Google Noto 字体 (简化版)
                'Source Han Sans SC', # 思源黑体
                'AR PL UMing CN',   # 文鼎PL细上海宋
                'AR PL UKai CN',    # 文鼎PL中楷
                'DejaVu Sans'       # 备用字体
            ]

            # 查找可用的中文字体
            available_fonts = [f.name for f in fm.fontManager.ttflist]
            selected_font = None

            # 打印可用字体信息（调试用）
            print("正在查找中文字体...")
            chinese_available = [font for font in available_fonts if any(
                chinese in font for chinese in ['SimHei', 'YaHei', 'PingFang', 'Hiragino',
                'WenQuanYi', 'Noto', 'Source Han', 'AR PL', 'Droid Sans Fallback']
            )]
            if chinese_available:
                print(f"发现可用的中文相关字体: {chinese_available[:5]}")  # 只显示前5个

            for font in chinese_fonts:
                if font in available_fonts:
                    selected_font = font
                    break

            if selected_font:
                plt.rcParams['font.sans-serif'] = [selected_font]
                print(f"使用字体: {selected_font}")
            else:
                # 如果没有找到中文字体，尝试使用英文标题
                print("警告: 未找到中文字体，将使用英文标题")
                print("建议安装中文字体包:")
                print("Ubuntu/Debian: sudo apt-get install fonts-wqy-microhei fonts-wqy-zenhei")
                print("CentOS/RHEL: sudo yum install wqy-microhei-fonts wqy-zenhei-fonts")
                print("或者: sudo apt-get install fonts-noto-cjk")

                # 使用英文标题的标志
                self.use_english_titles = True
                plt.rcParams['font.sans-serif'] = ['DejaVu Sans']

            plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
            
            # 创建DataFrame
            df = pd.DataFrame([
                {
                    '模式': r.mode,
                    '进程数': r.processes,
                    '线程数': r.threads_per_process,
                    '并发数': r.concurrent_packets,
                    '成功数': r.successful_packets,
                    '成功率': r.successful_packets / r.concurrent_packets
                }
                for r in self.results
            ])
            
            # 创建图表
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))

            # 根据字体支持情况选择标题
            if hasattr(self, 'use_english_titles') and self.use_english_titles:
                main_title = 'Race Condition Vulnerability Analysis Report'
                sync_title = 'Sync Mode - Successful Packets Analysis'
                thread_title = 'Multi-thread Mode - Successful Packets Analysis'
                async_title = 'Async Mode - Successful Packets Analysis'
                compare_title = 'Success Rate Comparison Analysis'
                xlabel_concurrent = 'Concurrent Packet Count'
                ylabel_success = 'Successful Packet Count'
                ylabel_rate = 'Success Rate (%)'
                watermark = 'Race Condition Analysis Report'
            else:
                main_title = '竞态条件漏洞利用分析报告'
                sync_title = '同步模式 - 成功数据包分析'
                thread_title = '多线程模式 - 成功数据包分析'
                async_title = '异步模式 - 成功数据包分析'
                compare_title = '各模式成功率对比分析'
                xlabel_concurrent = '并发数据包数量'
                ylabel_success = '成功获取数据包数量'
                ylabel_rate = '成功率 (%)'
                watermark = '竞态条件漏洞分析报告'

            fig.suptitle(main_title, fontsize=18, fontweight='bold', y=0.98)

            # 设置颜色方案
            colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b']

            # 1. 同步模式分析
            sync_data = df[df['模式'] == 'sync']
            if not sync_data.empty:
                color_idx = 0
                for workers in sorted(sync_data['进程数'].unique()):
                    worker_data = sync_data[sync_data['进程数'] == workers]
                    axes[0, 0].plot(worker_data['并发数'], worker_data['成功数'],
                                   marker='o', label=f'{workers}个进程', linewidth=2.5,
                                   markersize=6, color=colors[color_idx % len(colors)])
                    color_idx += 1

                axes[0, 0].set_title(sync_title, fontsize=14, fontweight='bold', pad=20)
                axes[0, 0].set_xlabel(xlabel_concurrent, fontsize=12)
                axes[0, 0].set_ylabel(ylabel_success, fontsize=12)
                axes[0, 0].legend(frameon=True, fancybox=True, shadow=True)
                axes[0, 0].grid(True, alpha=0.3, linestyle='--')
            
            # 2. 多线程模式分析
            gthread_data = df[df['模式'] == 'gthread']
            if not gthread_data.empty:
                color_idx = 0
                for _, group in gthread_data.groupby(['进程数', '线程数']):
                    workers = group['进程数'].iloc[0]
                    threads = group['线程数'].iloc[0]
                    axes[0, 1].plot(group['并发数'], group['成功数'],
                                   marker='s', label=f'{workers}进程×{threads}线程', linewidth=2.5,
                                   markersize=6, color=colors[color_idx % len(colors)])
                    color_idx += 1

                axes[0, 1].set_title(thread_title, fontsize=14, fontweight='bold', pad=20)
                axes[0, 1].set_xlabel(xlabel_concurrent, fontsize=12)
                axes[0, 1].set_ylabel(ylabel_success, fontsize=12)
                axes[0, 1].legend(frameon=True, fancybox=True, shadow=True)
                axes[0, 1].grid(True, alpha=0.3, linestyle='--')
            
            # 3. 异步模式分析
            gevent_data = df[df['模式'] == 'gevent']
            if not gevent_data.empty:
                axes[1, 0].plot(gevent_data['并发数'], gevent_data['成功数'],
                               marker='^', color='#d62728', label='Gevent异步模式',
                               linewidth=3, markersize=8, alpha=0.8)
                axes[1, 0].set_title(async_title, fontsize=14, fontweight='bold', pad=20)
                axes[1, 0].set_xlabel(xlabel_concurrent, fontsize=12)
                axes[1, 0].set_ylabel(ylabel_success, fontsize=12)
                axes[1, 0].legend(frameon=True, fancybox=True, shadow=True)
                axes[1, 0].grid(True, alpha=0.3, linestyle='--')
            
            # 4. 成功率对比
            color_idx = 0
            for mode in ['sync', 'gthread', 'gevent']:
                mode_data = df[df['模式'] == mode]
                if not mode_data.empty:
                    if mode == 'sync':
                        # 同步模式按进程数分组
                        for workers in sorted(mode_data['进程数'].unique()):
                            worker_data = mode_data[mode_data['进程数'] == workers]
                            axes[1, 1].plot(worker_data['并发数'], worker_data['成功率'],
                                           marker='o', label=f'同步-{workers}进程',
                                           linewidth=2.5, markersize=6,
                                           color=colors[color_idx % len(colors)], alpha=0.8)
                            color_idx += 1
                    elif mode == 'gthread':
                        # 多线程模式按配置分组
                        for _, group in mode_data.groupby(['进程数', '线程数']):
                            workers = group['进程数'].iloc[0]
                            threads = group['线程数'].iloc[0]
                            axes[1, 1].plot(group['并发数'], group['成功率'],
                                           marker='s', label=f'多线程-{workers}×{threads}',
                                           linewidth=2.5, markersize=6,
                                           color=colors[color_idx % len(colors)], alpha=0.8)
                            color_idx += 1
                    else:
                        # 异步模式
                        axes[1, 1].plot(mode_data['并发数'], mode_data['成功率'],
                                       marker='^', label='异步-Gevent', linewidth=3,
                                       markersize=8, color='#d62728', alpha=0.8)

            axes[1, 1].set_title(compare_title, fontsize=14, fontweight='bold', pad=20)
            axes[1, 1].set_xlabel(xlabel_concurrent, fontsize=12)
            axes[1, 1].set_ylabel(ylabel_rate, fontsize=12)
            axes[1, 1].legend(bbox_to_anchor=(1.05, 1), loc='upper left',
                             frameon=True, fancybox=True, shadow=True)
            axes[1, 1].grid(True, alpha=0.3, linestyle='--')
            axes[1, 1].set_ylim(0, 1.1)

            # 格式化Y轴为百分比
            axes[1, 1].yaxis.set_major_formatter(plt.FuncFormatter(lambda y, _: '{:.0%}'.format(y)))
            
            # 调整布局和样式
            plt.tight_layout(rect=[0, 0.03, 1, 0.95])  # 为主标题留出空间

            # 添加水印和说明
            fig.text(0.99, 0.01, '竞态条件漏洞分析报告',
                    horizontalalignment='right', fontsize=10, alpha=0.7)

            # 保存高质量图片
            plt.savefig('evaluation_analysis.png', dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')
            print(f"图表已保存到: evaluation_analysis.png")
            print(f"图表分辨率: 300 DPI，支持中文显示")
            
        except ImportError as e:
            print(f"\n缺少必要的图表库，跳过图表生成")
            print(f"错误详情: {str(e)}")
            print("安装命令: pip install matplotlib pandas")
            print("\n如果需要中文支持，建议额外安装中文字体:")
            print("Windows: 系统自带SimHei、Microsoft YaHei字体")
            print("macOS: 系统自带PingFang SC字体")
            print("Linux: sudo apt-get install fonts-wqy-microhei fonts-wqy-zenhei")
        except Exception as e:
            print(f"\n图表生成失败: {str(e)}")
            print("可能的解决方案:")
            print("1. 检查matplotlib和pandas是否正确安装")
            print("2. 确保系统有中文字体支持")
            print("3. 检查数据是否完整")
    
def main():
    print("研究同步和异步条件对数据包最大成功利用率的影响")
    print("=" * 60)
    
    # 检查依赖
    try:
        import gunicorn
    except ImportError:
        print("缺少 gunicorn，正在安装...")
        subprocess.run([sys.executable, "-m", "pip", "install", "gunicorn"])
    
    validator = EvaluationTest()
    
    try:
        # 同步模式
        validator.sync_mode()
        
        # 多线程同步模式
        validator.gthread_mode()
        
        # 异步模式
        validator.gevent_mode()
        
        # 生成表格
        validator.generate_table()
        
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {str(e)}")

if __name__ == "__main__":
    main()
